# Gather Point - Production Environment Configuration
# Copy this file to .env and update the values for your production environment

# Application
APP_NAME="Gather Point"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://api.gatherpoint.com

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=gather_point_production
DB_USERNAME=your_db_username
DB_PASSWORD=your_secure_db_password

# Broadcasting
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# AWS (if using S3 for file storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Pusher (if using real-time features)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# JWT (for API authentication)
JWT_SECRET=your_jwt_secret_key_here
JWT_TTL=60

# URWAY Payment Gateway (Production)
URWAY_TERMINAL_ID=gatherpoin
URWAY_TERMINAL_PASSWORD=gather@4512
URWAY_MERCHANT_KEY=75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef
URWAY_BASE_URL=https://payments.urway-tech.com
URWAY_CURRENCY=SAR

# SMS Gateway (Msegat)
SMS_GATEWAY_TOKEN=07158616c9f86e5662ff28cb5d4f6d06
SMS_GATEWAY_URL=https://www.msegat.com/gw/sendsms.php

# File Upload Limits
UPLOAD_MAX_SIZE=10240  # 10MB in KB
IMAGE_MAX_SIZE=5120    # 5MB in KB
VIDEO_MAX_SIZE=51200   # 50MB in KB

# API Rate Limiting
API_RATE_LIMIT=60      # requests per minute
API_RATE_LIMIT_GUEST=30 # requests per minute for guests

# Security
BCRYPT_ROUNDS=12
HASH_VERIFY=true

# Monitoring & Analytics
SENTRY_LARAVEL_DSN=your_sentry_dsn_here
GOOGLE_ANALYTICS_ID=your_ga_id_here

# Backup Configuration
BACKUP_DISK=s3
BACKUP_NOTIFICATION_MAIL=<EMAIL>

# Performance
OPCACHE_ENABLE=true
REDIS_CLIENT=predis

# CORS Configuration
SANCTUM_STATEFUL_DOMAINS=gatherpoint.com,www.gatherpoint.com,app.gatherpoint.com
SESSION_DOMAIN=.gatherpoint.com

# Telescope (disable in production)
TELESCOPE_ENABLED=false

# Horizon (for queue management)
HORIZON_DOMAIN=admin.gatherpoint.com
HORIZON_PATH=horizon
