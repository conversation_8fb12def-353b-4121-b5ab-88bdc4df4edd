<?php

use App\Http\Controllers\Admin\AttendanceController;
use App\Http\Controllers\Admin\CityController;
use App\Http\Controllers\Admin\CountryController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\FacilitiesController;
use App\Http\Controllers\Admin\FacilitiesCategoryController;
use App\Http\Controllers\Admin\FinancialCategoryController;
use App\Http\Controllers\Admin\InvoiceController;
use App\Http\Controllers\Admin\LanguageController;
use App\Http\Controllers\Admin\PaymentMethodsController;
use App\Http\Controllers\Admin\ReportsController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\ServiceCategoryController;
use App\Http\Controllers\Admin\ServiceCategoryDocumentsController;
use App\Http\Controllers\Admin\ServiceCategoryFieldsController;
use App\Http\Controllers\Admin\ServiceCategoryFormController;
use App\Http\Controllers\Admin\ServiceCategoryItemController;
use App\Http\Controllers\Admin\ServiceCategoryItemDisablePeriodController;
use App\Http\Controllers\Admin\ServiceCategoryItemGalleyController;
use App\Http\Controllers\Admin\ServiceCategoryItemOfferController;
use App\Http\Controllers\Admin\ServiceCategoryItemReservationController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\TransactionsController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\PaymentPageController;
use Illuminate\Support\Facades\Route;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */
Route::get('/seed', function () {
    Artisan::call("db:seed --class=ResetPermissionSeeder");
});

Route::post('logout', [DashboardController::class, 'logout'])->name('logout');

// Payment Pages (No Auth Required)
Route::prefix('payment')->group(function () {
    Route::get('/success', [PaymentPageController::class, 'success'])->name('payment.success');
    Route::get('/error', [PaymentPageController::class, 'error'])->name('payment.error');
});

Route::middleware('auth')->group(function () {
    // locale
    Route::get('lang/{locale}', [LanguageController::class, 'swap']);

    // dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // roles routes
    Route::prefix('roles')->group(function () {
        Route::get('/', [RoleController::class, 'index'])->name('roles.index');
        Route::get('/create', [RoleController::class, 'create'])->name('roles.create');
        Route::post('/store', [RoleController::class, 'store'])->name('roles.store');
        Route::get('/edit/{id}', [RoleController::class, 'edit'])->name('roles.edit');
        Route::put('/update/{id}', [RoleController::class, 'update'])->name('roles.update');
        Route::delete('/destroy/{id}', [RoleController::class, 'destroy'])->name('roles.destroy');
    });

    // users routes
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('users.index');
        Route::get('/create', [UserController::class, 'create'])->name('users.create');
        Route::get('/create_ajax', [UserController::class, 'create_ajax'])->name('users.create_ajax');
        Route::post('/store', [UserController::class, 'store'])->name('users.store');
        Route::get('/edit/{id}', [UserController::class, 'edit'])->name('users.edit');
        Route::put('/update/{id}', [UserController::class, 'update'])->name('users.update');
        Route::delete('/destroy/{id}', [UserController::class, 'destroy'])->name('users.destroy');
        Route::get('/profile', [UserController::class, 'profile'])->name('users.profile.show');
        Route::get('/pages/profile-user', [UserController::class, 'profile'])->name('pages-profile-user');
        Route::get('/search', [UserController::class, 'autoComplete'])->name('users.search');
    });

    // service category items routes
    Route::prefix('service_category_items')->group(function () {
        Route::get('/', [ServiceCategoryItemController::class, 'index'])->name('service_category_items.index');
        Route::get('/create', [ServiceCategoryItemController::class, 'create'])->name('service_category_items.create');
        Route::post('/store', [ServiceCategoryItemController::class, 'store'])->name('service_category_items.store');
        Route::get('/edit/{id}', [ServiceCategoryItemController::class, 'edit'])->name('service_category_items.edit');
        Route::put('/update/{id}', [ServiceCategoryItemController::class, 'update'])->name('service_category_items.update');
        Route::delete('/destroy/{id}', [ServiceCategoryItemController::class, 'destroy'])->name('service_category_items.destroy');
        // Service Category Item Reservations Routes
        Route::prefix('{serviceCategoryItemId}/reservations')->group(function () {
            Route::get('/', [ServiceCategoryItemReservationController::class, 'index'])->name('service_category_item_reservations.index');
            Route::get('/create', [ServiceCategoryItemReservationController::class, 'create'])->name('service_category_item_reservations.create');
            Route::post('/', [ServiceCategoryItemReservationController::class, 'store'])->name('service_category_item_reservations.store');
            Route::get('/{reservationId}/edit', [ServiceCategoryItemReservationController::class, 'edit'])->name('service_category_item_reservations.edit');
            Route::put('/{reservationId}', [ServiceCategoryItemReservationController::class, 'update'])->name('service_category_item_reservations.update');
            Route::delete('/{reservationId}', [ServiceCategoryItemReservationController::class, 'destroy'])->name('service_category_item_reservations.destroy');
        });
        // Service Category Item Disabled periods Routes
        Route::prefix('{serviceCategoryItemId}/disable-periods')->group(function () {
            Route::get('/', [ServiceCategoryItemDisablePeriodController::class, 'index'])->name('service_category_item_disable_periods.index');
            Route::get('/create', [ServiceCategoryItemDisablePeriodController::class, 'create'])->name('service_category_item_disable_periods.create');
            Route::post('/', [ServiceCategoryItemDisablePeriodController::class, 'store'])->name('service_category_item_disable_periods.store');
            Route::get('/{disablePeriodId}/edit', [ServiceCategoryItemDisablePeriodController::class, 'edit'])->name('service_category_item_disable_periods.edit');
            Route::put('/{disablePeriodId}', [ServiceCategoryItemDisablePeriodController::class, 'update'])->name('service_category_item_disable_periods.update');
            Route::delete('/{disablePeriodId}', [ServiceCategoryItemDisablePeriodController::class, 'destroy'])->name('service_category_item_disable_periods.destroy');
        });
        // Service Category Item Galleries Routes
        Route::prefix('{serviceCategoryItemId}/item_galleries')->group(function () {
            Route::get('/', [ServiceCategoryItemGalleyController::class, 'index'])->name('service_category_item_galleries.index');
            Route::get('/create', [ServiceCategoryItemGalleyController::class, 'create'])->name('service_category_item_galleries.create');
            Route::post('/', [ServiceCategoryItemGalleyController::class, 'store'])->name('service_category_item_galleries.store');
            Route::get('/{galleryId}/edit', [ServiceCategoryItemGalleyController::class, 'edit'])->name('service_category_item_galleries.edit');
            Route::put('/{galleryId}', [ServiceCategoryItemGalleyController::class, 'update'])->name('service_category_item_galleries.update');
            Route::delete('/{galleryId}', [ServiceCategoryItemGalleyController::class, 'destroy'])->name('service_category_item_galleries.destroy');
        });
        // Service Category Item Offers Routes
        Route::prefix('{serviceCategoryItemId}/offers')->group(function () {
            Route::get('/', [ServiceCategoryItemOfferController::class, 'index'])->name('service_category_item_offers.index');
            Route::get('/create', [ServiceCategoryItemOfferController::class, 'create'])->name('service_category_item_offers.create');
            Route::post('/', [ServiceCategoryItemOfferController::class, 'store'])->name('service_category_item_offers.store');
            Route::get('/{offerId}/edit', [ServiceCategoryItemOfferController::class, 'edit'])->name('service_category_item_offers.edit');
            Route::put('/{offerId}', [ServiceCategoryItemOfferController::class, 'update'])->name('service_category_item_offers.update');
            Route::delete('/{offerId}', [ServiceCategoryItemOfferController::class, 'destroy'])->name('service_category_item_offers.destroy');
        });
    });

    // attendance routes
    Route::prefix('attendances')->group(function () {
        Route::get('/', [AttendanceController::class, 'index'])->name('attendances.index');
        Route::get('/create', [AttendanceController::class, 'create'])->name('attendances.create');
        Route::post('/store', [AttendanceController::class, 'store'])->name('attendances.store');
        Route::delete('/destroy/{id}', [AttendanceController::class, 'destroy'])->name('attendances.destroy');
    });

    // invoices routes
    Route::prefix('invoices')->group(function () {
        Route::get('/', [InvoiceController::class, 'index'])->name('invoices.index');
        Route::get('/shift/{id}', [InvoiceController::class, 'shift'])->name('invoices.shift');
        Route::get('/create', [InvoiceController::class, 'create'])->name('invoices.create');
        Route::post('/store', [InvoiceController::class, 'store'])->name('invoices.store');
        Route::post('/pay/{id}', [InvoiceController::class, 'pay'])->name('invoices.pay');
        Route::delete('/destroy/{id}', [InvoiceController::class, 'destroy'])->name('invoices.destroy');
    });

    // transactions routes
    Route::prefix('transactions')->group(function () {
        Route::get('/', [TransactionsController::class, 'index'])->name('transactions.index');
        Route::get('/create', [TransactionsController::class, 'create'])->name('transactions.create');
        Route::post('/store', [TransactionsController::class, 'store'])->name('transactions.store');
        Route::post('/store_cash_in', [TransactionsController::class, 'store_cash_in'])->name('transactions.store_cash_in');
        Route::post('/store_cash_out', [TransactionsController::class, 'store_cash_out'])->name('transactions.store_cash_out');
        Route::get('/show/{id}', [TransactionsController::class, 'show'])->name('transactions.show');
        Route::get('/edit/{id}', [TransactionsController::class, 'edit'])->name('transactions.edit');
        Route::put('/update/{id}', [TransactionsController::class, 'update'])->name('transactions.update');
        Route::delete('/destroy/{id}', [TransactionsController::class, 'destroy'])->name('transactions.destroy');
        Route::post('/change_status/{id}', [TransactionsController::class, 'change_status'])->name('transactions.change_status');
    });

    // reports routes
    Route::prefix('reports')->group(function () {
        // cashier
        Route::get('/cashier', [ReportsController::class, 'cashier'])->name('reports.cashier.index');
        // cashier In&Out
        Route::get('/cashier_in_out', [ReportsController::class, 'cashier_in_out'])->name('reports.cashier_in_out.index');
        // audit log
        Route::get('/audit_log', [ReportsController::class, 'audit_log'])->name('reports.audit_log.index');
    });

    // settings routes
    Route::prefix('settings')->group(function () {
        // settings
        Route::get('/edit', [SettingsController::class, 'getSettings'])->name('settings.edit');
        Route::put('/update', [SettingsController::class, 'updateSettings'])->name('settings.update');

        // email_setting
        Route::prefix('email')->group(function () {
            Route::get('/edit', [SettingsController::class, 'getEmailSetting'])->name('settings.email.edit');
            Route::put('/update', [SettingsController::class, 'updateEmailSetting'])->name('settings.email.update');
        });

        // credential_setting
        Route::prefix('credential')->group(function () {
            Route::get('/edit', [SettingsController::class, 'editCredentialSetting'])->name('settings.credential.edit');
            Route::put('/update', [SettingsController::class, 'updateCredentialSetting'])->name('settings.credential.update');
        });
        // sms_setting
        Route::prefix('sms')->group(function () {
            Route::get('/edit', [SettingsController::class, 'getSmsSetting'])->name('settings.sms.edit');
            Route::put('/update', [SettingsController::class, 'updateSmsSetting'])->name('settings.sms.update');
        });
        // Payment Gateway Settings Routes
        Route::prefix('payment_gateway')->group(function () {
            Route::get('/edit', [SettingsController::class, 'getPaymentGatewaySetting'])->name('settings.payment_gateway.edit');
            Route::put('/update', [SettingsController::class, 'updatePaymentGatewaySetting'])->name('settings.payment_gateway.update');
        });
        // financial categories
        Route::prefix('financial_categories')->group(function () {
            Route::get('/', [FinancialCategoryController::class, 'index'])->name('financial_categories.index');
            Route::get('/create', [FinancialCategoryController::class, 'create'])->name('financial_categories.create');
            Route::post('/store', [FinancialCategoryController::class, 'store'])->name('financial_categories.store');
            Route::get('/edit/{id}', [FinancialCategoryController::class, 'edit'])->name('financial_categories.edit');
            Route::put('/update/{id}', [FinancialCategoryController::class, 'update'])->name('financial_categories.update');
            Route::delete('/destroy/{id}', [FinancialCategoryController::class, 'destroy'])->name('financial_categories.destroy');
        });
        // service categories
        Route::prefix('service_categories')->group(function () {
            Route::get('/', [ServiceCategoryController::class, 'index'])->name('service_categories.index');
            Route::get('/create', [ServiceCategoryController::class, 'create'])->name('service_categories.create');
            Route::post('/store', [ServiceCategoryController::class, 'store'])->name('service_categories.store');
            Route::get('/edit/{id}', [ServiceCategoryController::class, 'edit'])->name('service_categories.edit');
            Route::put('/update/{id}', [ServiceCategoryController::class, 'update'])->name('service_categories.update');
            Route::delete('/destroy/{id}', [ServiceCategoryController::class, 'destroy'])->name('service_categories.destroy');
            Route::prefix('{serviceCategoryId}/forms')->group(function () {
                Route::get('/', [ServiceCategoryFormController::class, 'index'])->name('service_categories_forms.index');
                Route::get('/create', [ServiceCategoryFormController::class, 'create'])->name('service_categories_forms.create');
                Route::post('/', [ServiceCategoryFormController::class, 'store'])->name('service_categories_forms.store');
                Route::get('/{formId}/edit', [ServiceCategoryFormController::class, 'edit'])->name('service_categories_forms.edit');
                Route::put('/{formId}', [ServiceCategoryFormController::class, 'update'])->name('service_categories_forms.update');
                Route::delete('/{formId}', [ServiceCategoryFormController::class, 'destroy'])->name('service_categories_forms.destroy');
            });
            Route::get('/get-fields-and-documents/{service_category_id}/{type}', [ServiceCategoryFormController::class, 'getFieldsAndDocuments']);
        });
        // service categories fields
        Route::prefix('service_categories_fields')->group(function () {
            Route::get('/', [ServiceCategoryFieldsController::class, 'index'])->name('service_categories_fields.index');
            Route::get('/create', [ServiceCategoryFieldsController::class, 'create'])->name('service_categories_fields.create');
            Route::post('/store', [ServiceCategoryFieldsController::class, 'store'])->name('service_categories_fields.store');
            Route::get('/edit/{id}', [ServiceCategoryFieldsController::class, 'edit'])->name('service_categories_fields.edit');
            Route::put('/update/{id}', [ServiceCategoryFieldsController::class, 'update'])->name('service_categories_fields.update');
            Route::delete('/destroy/{id}', [ServiceCategoryFieldsController::class, 'destroy'])->name('service_categories_fields.destroy');
        });
        // service categories documents
        Route::prefix('service_categories_documents')->group(function () {
            Route::get('/', [ServiceCategoryDocumentsController::class, 'index'])->name('service_categories_documents.index');
            Route::get('/create', [ServiceCategoryDocumentsController::class, 'create'])->name('service_categories_documents.create');
            Route::post('/store', [ServiceCategoryDocumentsController::class, 'store'])->name('service_categories_documents.store');
            Route::get('/edit/{id}', [ServiceCategoryDocumentsController::class, 'edit'])->name('service_categories_documents.edit');
            Route::put('/update/{id}', [ServiceCategoryDocumentsController::class, 'update'])->name('service_categories_documents.update');
            Route::delete('/destroy/{id}', [ServiceCategoryDocumentsController::class, 'destroy'])->name('service_categories_documents.destroy');
        });
        // payment_methods
        Route::prefix('payment_methods')->group(function () {
            Route::get('/', [PaymentMethodsController::class, 'index'])->name('payment_methods.index');
            Route::get('/create', [PaymentMethodsController::class, 'create'])->name('payment_methods.create');
            Route::post('/store', [PaymentMethodsController::class, 'store'])->name('payment_methods.store');
            Route::get('/edit/{id}', [PaymentMethodsController::class, 'edit'])->name('payment_methods.edit');
            Route::put('/update/{id}', [PaymentMethodsController::class, 'update'])->name('payment_methods.update');
            Route::delete('/destroy/{id}', [PaymentMethodsController::class, 'destroy'])->name('payment_methods.destroy');
        });
        // countries
        Route::prefix('countries')->group(function () {
            Route::get('/', [CountryController::class, 'index'])->name('countries.index');
            Route::get('/create', [CountryController::class, 'create'])->name('countries.create');
            Route::post('/store', [CountryController::class, 'store'])->name('countries.store');
            Route::get('/edit/{id}', [CountryController::class, 'edit'])->name('countries.edit');
            Route::put('/update/{id}', [CountryController::class, 'update'])->name('countries.update');
            Route::delete('/destroy/{id}', [CountryController::class, 'destroy'])->name('countries.destroy');
        });
        // cities
        Route::prefix('cities')->group(function () {
            Route::get('/', [CityController::class, 'index'])->name('cities.index');
            Route::get('/create', [CityController::class, 'create'])->name('cities.create');
            Route::post('/store', [CityController::class, 'store'])->name('cities.store');
            Route::get('/edit/{id}', [CityController::class, 'edit'])->name('cities.edit');
            Route::put('/update/{id}', [CityController::class, 'update'])->name('cities.update');
            Route::delete('/destroy/{id}', [CityController::class, 'destroy'])->name('cities.destroy');
        });
        // Facilities
        Route::prefix('facilities')->group(function () {
            Route::get('/', [FacilitiesController::class, 'index'])->name('facilities.index');
            Route::get('/create', [FacilitiesController::class, 'create'])->name('facilities.create');
            Route::post('/store', [FacilitiesController::class, 'store'])->name('facilities.store');
            Route::get('/edit/{id}', [FacilitiesController::class, 'edit'])->name('facilities.edit');
            Route::put('/update/{id}', [FacilitiesController::class, 'update'])->name('facilities.update');
            Route::delete('/destroy/{id}', [FacilitiesController::class, 'destroy'])->name('facilities.destroy');
        });
        // facilities categories routes
        Route::prefix('facilities_categories')->group(function () {
            Route::get('/', [FacilitiesCategoryController::class, 'index'])->name('facilities_categories.index');
            Route::get('/create', [FacilitiesCategoryController::class, 'create'])->name('facilities_categories.create');
            Route::post('/store', [FacilitiesCategoryController::class, 'store'])->name('facilities_categories.store');
            Route::get('/edit/{id}', [FacilitiesCategoryController::class, 'edit'])->name('facilities_categories.edit');
            Route::put('/update/{id}', [FacilitiesCategoryController::class, 'update'])->name('facilities_categories.update');
            Route::delete('/destroy/{id}', [FacilitiesCategoryController::class, 'destroy'])->name('facilities_categories.destroy');
        });
        // property_types
        Route::prefix('property_types')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'index'])->name('property_types.index');
            Route::get('/create', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'create'])->name('property_types.create');
            Route::post('/store', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'store'])->name('property_types.store');
            Route::get('/edit/{id}', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'edit'])->name('property_types.edit');
            Route::put('/update/{id}', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'update'])->name('property_types.update');
            Route::delete('/destroy/{id}', [\App\Http\Controllers\Admin\PropertyTypeController::class, 'destroy'])->name('property_types.destroy');
        });
    });

});
