# URWAY Payment Gateway - Production Setup

## 🚀 Production Configuration

### Current Settings
- **Environment**: Production
- **Base URL**: `https://payments.urway-tech.com`
- **Terminal ID**: `gatherpoin`
- **Terminal Password**: `gather@4512`
- **Merchant Key**: `75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef`
- **Currency**: SAR (Saudi Riyal)

### 📋 Production Deployment Checklist

#### 1. URWAY Account Setup
- [ ] Verify URWAY merchant account is activated for production
- [ ] Confirm Terminal ID and Password are correct for production environment
- [ ] Ensure Merchant Key is valid and active
- [ ] Verify account has necessary permissions for transactions

#### 2. Domain & IP Whitelisting
- [ ] Add your production domain to URWAY whitelist
- [ ] Add your server IP address to URWAY whitelist (if required)
- [ ] Configure callback URL in URWAY dashboard: `https://yourdomain.com/api/payments/urway/callback`

#### 3. SSL & Security
- [ ] Ensure your domain has valid SSL certificate
- [ ] Verify HTTPS is enforced for all payment-related endpoints
- [ ] Test callback URL is accessible from URWAY servers

#### 4. Environment Variables
Update your `.env` file with production settings:
```env
APP_URL=https://yourdomain.com
APP_ENV=production
APP_DEBUG=false
```

#### 5. Database Configuration
Run the seeder to update production settings:
```bash
php artisan db:seed --class=PaymentGatewaySeeder
```

#### 6. Testing Checklist
- [ ] Test payment initiation API endpoint
- [ ] Test payment status checking
- [ ] Test URWAY callback handling
- [ ] Test success/error page redirects
- [ ] Verify payment confirmation emails
- [ ] Test reservation confirmation after payment

### 🔧 Commands for Production Setup

```bash
# 1. Update payment gateway configuration
php artisan db:seed --class=PaymentGatewaySeeder

# 2. Test URWAY credentials
php artisan test:urway-credentials

# 3. Test payment flow
php artisan test:urway-payment --amount=10.00

# 4. Clear caches
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 🌐 API Endpoints

#### Payment Initiation
```
POST /api/payments/initiate
Authorization: Bearer {token}
Content-Type: application/json

{
    "reservation_id": 123,
    "amount": 150.00,
    "customer_email": "<EMAIL>"
}
```

#### Payment Status Check
```
POST /api/payments/status
Authorization: Bearer {token}
Content-Type: application/json

{
    "payment_id": "GP_UNIQUEID123"
}
```

#### URWAY Callback (No Auth)
```
POST /api/payments/urway/callback
Content-Type: application/x-www-form-urlencoded

# URWAY will send payment result data
```

### 🎨 Payment Pages
- **Success Page**: `https://yourdomain.com/payment/success?payment_id={id}`
- **Error Page**: `https://yourdomain.com/payment/error?payment_id={id}`

### 📱 Mobile App Integration

The mobile app should:
1. Call `/api/payments/initiate` to get payment URL
2. Open payment URL in WebView or external browser
3. Monitor for success/error page redirects
4. Call `/api/payments/status` to verify payment completion
5. Update UI based on payment status

### 🔍 Troubleshooting

#### Common Issues:
1. **Error 601**: Authentication failed - check credentials
2. **Error 404**: Endpoint not found - verify URL
3. **SSL Issues**: Ensure valid certificate
4. **Callback not received**: Check IP whitelisting

#### Debug Commands:
```bash
# Check logs
tail -f storage/logs/laravel.log

# Test credentials
php artisan test:urway-credentials

# Test with debug logging
php artisan test:urway-payment --amount=1.00
```

### 📞 URWAY Support Contact

If you encounter issues:
1. Contact URWAY technical support
2. Provide your Terminal ID: `gatherpoin`
3. Mention you're setting up production environment
4. Share any error codes (like 601) you're receiving

### 🔒 Security Notes

- Never expose Terminal Password or Merchant Key in client-side code
- Always use HTTPS for payment-related requests
- Validate all callback data using hash verification
- Log all payment transactions for audit purposes
- Implement proper error handling and user feedback

---

## 🚀 Ready for Production!

Once URWAY confirms your account is activated and properly configured, your payment system will be ready to process real transactions in production.
