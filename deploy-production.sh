#!/bin/bash

# Gather Point - Production Deployment Script
# This script prepares the application for production deployment

set -e  # Exit on any error

echo "🚀 Gather Point - Production Deployment"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

print_info "Starting production deployment process..."

# 1. Environment Check
echo ""
echo "1. Environment Configuration"
echo "----------------------------"

if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it from .env.example"
    exit 1
fi

# Check critical environment variables
ENV_VARS=("APP_ENV" "APP_KEY" "DB_CONNECTION" "DB_HOST" "DB_DATABASE")
for var in "${ENV_VARS[@]}"; do
    if grep -q "^${var}=" .env; then
        print_status "${var} is set"
    else
        print_warning "${var} is not set in .env"
    fi
done

# 2. Dependencies
echo ""
echo "2. Installing Dependencies"
echo "-------------------------"

print_info "Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader
print_status "Composer dependencies installed"

# 3. Database Setup
echo ""
echo "3. Database Configuration"
echo "------------------------"

print_info "Running database migrations..."
php artisan migrate --force
print_status "Database migrations completed"

print_info "Setting up payment gateway..."
php artisan db:seed --class=PaymentGatewaySeeder
print_status "Payment gateway configured"

# 4. Caching and Optimization
echo ""
echo "4. Performance Optimization"
echo "---------------------------"

print_info "Clearing existing caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

print_info "Building production caches..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
print_status "Application optimized for production"

# 5. File Permissions
echo ""
echo "5. Setting File Permissions"
echo "---------------------------"

print_info "Setting proper file permissions..."
chmod -R 755 storage bootstrap/cache
chmod -R 644 storage/logs
print_status "File permissions set"

# 6. Storage Link
echo ""
echo "6. Storage Configuration"
echo "-----------------------"

if [ ! -L "public/storage" ]; then
    print_info "Creating storage symlink..."
    php artisan storage:link
    print_status "Storage symlink created"
else
    print_status "Storage symlink already exists"
fi

# 7. URWAY Testing
echo ""
echo "7. Payment Gateway Testing"
echo "-------------------------"

print_info "Testing URWAY credentials..."
php artisan test:urway-credentials

# 8. Final Checks
echo ""
echo "8. Final Production Checks"
echo "-------------------------"

# Check if APP_DEBUG is false
if grep -q "APP_DEBUG=false" .env; then
    print_status "APP_DEBUG is set to false"
else
    print_warning "APP_DEBUG should be set to false in production"
fi

# Check if APP_ENV is production
if grep -q "APP_ENV=production" .env; then
    print_status "APP_ENV is set to production"
else
    print_warning "APP_ENV should be set to production"
fi

# 9. Security Recommendations
echo ""
echo "9. Security Checklist"
echo "--------------------"

echo "Please ensure the following:"
echo "• SSL certificate is installed and configured"
echo "• HTTPS is enforced for all requests"
echo "• Database credentials are secure"
echo "• Server firewall is properly configured"
echo "• Regular backups are scheduled"
echo "• Monitoring and logging are set up"

# 10. URWAY Production Setup
echo ""
echo "10. URWAY Production Requirements"
echo "--------------------------------"

echo "Contact URWAY support to ensure:"
echo "• Your domain is whitelisted"
echo "• Your server IP is whitelisted (if required)"
echo "• Terminal ID 'gatherpoin' is activated for production"
echo "• Merchant account is approved and active"
echo "• Callback URL is configured properly"

# 11. Testing Commands
echo ""
echo "11. Post-Deployment Testing"
echo "--------------------------"

echo "Run these commands to test your deployment:"
echo "php artisan test:urway-credentials"
echo "php artisan test:urway-payment --amount=1.00"

# Summary
echo ""
echo "🎉 Production Deployment Summary"
echo "==============================="

echo "✅ Dependencies installed"
echo "✅ Database configured"
echo "✅ Payment gateway set up"
echo "✅ Application optimized"
echo "✅ File permissions set"
echo "✅ Storage configured"

echo ""
print_info "Deployment completed successfully!"
print_warning "Remember to test thoroughly before going live!"

echo ""
echo "📞 Support Information:"
echo "• URWAY Terminal ID: gatherpoin"
echo "• Payment Currency: SAR"
echo "• Production URL: https://payments.urway-tech.com"

echo ""
echo "🔗 Important URLs:"
echo "• Payment Success: https://yourdomain.com/payment/success"
echo "• Payment Error: https://yourdomain.com/payment/error"
echo "• URWAY Callback: https://yourdomain.com/api/payments/urway/callback"

echo ""
print_info "Production deployment script completed!"
