URWAY Payment Gateway - Support Request Data
===========================================

Dear URWAY Support Team,

We are experiencing authentication issues with our payment integration and need your assistance.

ACCOUNT INFORMATION:
- Terminal ID: gatherpoin
- Environment: Production (https://payments.urway-tech.com)
- Error Codes: 659 (Request authentication failed), 601 (System Error)

TECHNICAL DETAILS:
- Integration Type: REST API JSON
- Request Method: POST
- Request URL: https://payments.urway-tech.com/URWAYPGService/transaction/jsonProcess/JSONrequest
- Content-Type: application/json

SAMPLE JSON REQUEST DATA:
========================
{
    "trackid": "SUPPORT_1750766088",
    "terminalId": "gatherpoin",
    "password": "gather@4512",
    "action": "1",
    "merchantIp": "127.0.0.1",
    "currency": "SAR",
    "country": "SA",
    "amount": "100",
    "customerEmail": "<EMAIL>",
    "udf1": "123",
    "udf2": "456",
    "udf3": "reservation_payment",
    "udf4": "",
    "udf5": "",
    "requestHash": "a63f330d5eb1dc1a75bc654854e49bc45ff93029f5021cabb1c49dbac4991c1e"
}

HASH GENERATION:
===============
Hash String Format: trackid|terminalId|password|merchantKey|amount|currency
Hash String: SUPPORT_1750766088|gatherpoin|gather@4512|75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef|100|SAR
Hash Algorithm: SHA256
Generated Hash: a63f330d5eb1dc1a75bc654854e49bc45ff93029f5021cabb1c49dbac4991c1e

CURRENT RESPONSE:
================
{
    "tranid": "",
    "result": "UnSuccessful",
    "reason": "Request authentication failed.",
    "authCode": "",
    "trackid": "",
    "terminalid": "",
    "udf1": "",
    "cardBrand": "",
    "udf2": "",
    "responseCode": "659",
    "rrn": ""
}

QUESTIONS FOR SUPPORT:
=====================
1. Is the merchant account "gatherpoin" activated for production transactions?
2. Are the provided credentials (Terminal ID, Password, Merchant Key) correct for production?
3. Is there any additional setup or documentation required?
4. Is IP whitelisting required? If so, what IPs need to be whitelisted?
5. Can you confirm the correct request format for the JSON API?
6. Are there any missing required fields in our request?

IMPLEMENTATION REFERENCE:
========================
Our implementation is based on the Laravel URWAY package:
https://github.com/AbdallaMohammed/laravel-urway

We have followed the exact same request format and hash generation method.

Please let us know what needs to be corrected to resolve the authentication error.

Thank you for your assistance.

Best regards,
Gather Point Development Team
