import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/feature/payments/data/services/urway_payment_service.dart';
import 'package:gather_point/feature/payments/data/models/payment_transaction_model.dart';

part 'payment_state.dart';

class PaymentCubit extends Cubit<PaymentState> {
  final UrwayPaymentService _urwayPaymentService;

  PaymentCubit(this._urwayPaymentService) : super(PaymentInitial());

  /// Process payment through URWAY
  Future<void> processPayment({
    required BuildContext context,
    required int reservationId,
    required double amount,
    String? customerEmail,
  }) async {
    if (state is PaymentLoading) return;

    emit(PaymentLoading());

    try {
      // Add timeout to prevent indefinite loading
      final result = await _urwayPaymentService.processPayment(
        context: context,
        reservationId: reservationId,
        amount: amount,
        customerEmail: customerEmail,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Payment request timed out');
        },
      );

      if (result.isSuccess) {
        emit(PaymentSuccess(
          paymentId: result.paymentId!,
          amount: result.amount!,
          currency: result.currency!,
        ));
      } else if (result.isFailed) {
        emit(PaymentFailed(result.message));
      } else if (result.isCancelled) {
        emit(PaymentCancelled());
      } else if (result.isPending) {
        emit(PaymentPending(
          paymentId: result.paymentId!,
          message: result.message,
        ));

        // Start polling for payment status
        _pollPaymentStatus(result.paymentId!);
      }
    } catch (e) {
      emit(PaymentFailed('Payment processing failed: ${e.toString()}'));
    }
  }

  /// Poll payment status for pending payments
  Future<void> _pollPaymentStatus(String paymentId) async {
    try {
      final status = await _urwayPaymentService.pollPaymentStatus(
        paymentId: paymentId,
      );

      if (status == PaymentStatus.completed) {
        // Get updated payment details
        final response = await _urwayPaymentService.paymentApiService.checkPaymentStatus(
          paymentId: paymentId,
        );

        if (response.success && response.data != null) {
          emit(PaymentSuccess(
            paymentId: paymentId,
            amount: response.data!.amount,
            currency: response.data!.currency,
          ));
        } else {
          emit(PaymentFailed('Payment verification failed'));
        }
      } else if (status == PaymentStatus.failed || status == PaymentStatus.cancelled) {
        emit(PaymentFailed('Payment was not completed'));
      } else {
        // Still pending - could implement timeout logic here
        emit(PaymentFailed('Payment timeout'));
      }
    } catch (e) {
      emit(PaymentFailed('Payment status check failed: ${e.toString()}'));
    }
  }

  /// Check payment status manually
  Future<void> checkPaymentStatus(String paymentId) async {
    emit(PaymentLoading());

    try {
      final response = await _urwayPaymentService.paymentApiService.checkPaymentStatus(
        paymentId: paymentId,
      );

      if (response.success && response.data != null) {
        final data = response.data!;
        
        switch (data.status) {
          case PaymentStatus.completed:
            emit(PaymentSuccess(
              paymentId: data.paymentId,
              amount: data.amount,
              currency: data.currency,
            ));
            break;
          case PaymentStatus.failed:
          case PaymentStatus.cancelled:
            emit(PaymentFailed('Payment was not completed'));
            break;
          case PaymentStatus.pending:
          case PaymentStatus.processing:
            emit(PaymentPending(
              paymentId: data.paymentId,
              message: 'Payment is being processed',
            ));
            break;
          default:
            emit(PaymentFailed('Unknown payment status'));
        }
      } else {
        emit(PaymentFailed(response.message));
      }
    } catch (e) {
      emit(PaymentFailed('Failed to check payment status: ${e.toString()}'));
    }
  }

  /// Reset payment state
  void resetPayment() {
    print('🔄 PaymentCubit.resetPayment() called');
    emit(PaymentInitial());
    print('✅ PaymentCubit reset to PaymentInitial');
  }


}
