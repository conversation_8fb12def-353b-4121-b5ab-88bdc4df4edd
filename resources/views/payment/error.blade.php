<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - Gather Point</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        .error-icon {
            width: 80px;
            height: 80px;
            background: #f44336;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 40px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .error-details {
            background: #fff3f3;
            border: 1px solid #ffebee;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ffebee;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .btn {
            background: #fec53a;
            color: #333;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .btn:hover {
            background: #e6b034;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-left: 10px;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✗</div>
        <h1>Payment Failed</h1>
        <p>Unfortunately, your payment could not be processed. Please try again or contact support if the problem persists.</p>
        
        @if($payment)
        <div class="error-details">
            <div class="detail-row">
                <span><strong>Payment ID:</strong></span>
                <span>{{ $payment->payment_id }}</span>
            </div>
            <div class="detail-row">
                <span><strong>Amount:</strong></span>
                <span>{{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
            </div>
            <div class="detail-row">
                <span><strong>Status:</strong></span>
                <span style="color: #f44336; font-weight: 600;">{{ ucfirst($payment->status) }}</span>
            </div>
            @if($payment->failure_reason)
            <div class="detail-row">
                <span><strong>Reason:</strong></span>
                <span>{{ $payment->failure_reason }}</span>
            </div>
            @endif
        </div>
        @endif
        
        <p><strong>Error:</strong> {{ $error_message }}</p>
        
        <p>You can close this window and try again from the app.</p>
        
        <script>
            // Auto-close after 10 seconds if opened in popup
            if (window.opener) {
                setTimeout(() => {
                    window.close();
                }, 10000);
            }
        </script>
    </div>
</body>
</html>
