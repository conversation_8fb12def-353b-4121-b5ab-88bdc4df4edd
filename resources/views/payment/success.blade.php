<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Gather Point</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 40px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .btn {
            background: #fec53a;
            color: #333;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .btn:hover {
            background: #e6b034;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>Payment Successful!</h1>
        <p>Your payment has been processed successfully. You will receive a confirmation email shortly.</p>
        
        @if($payment)
        <div class="payment-details">
            <div class="detail-row">
                <span><strong>Payment ID:</strong></span>
                <span>{{ $payment->payment_id }}</span>
            </div>
            <div class="detail-row">
                <span><strong>Amount:</strong></span>
                <span>{{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
            </div>
            <div class="detail-row">
                <span><strong>Status:</strong></span>
                <span style="color: #4CAF50; font-weight: 600;">{{ ucfirst($payment->status) }}</span>
            </div>
            @if($payment->paid_at)
            <div class="detail-row">
                <span><strong>Paid At:</strong></span>
                <span>{{ $payment->paid_at->format('M d, Y H:i') }}</span>
            </div>
            @endif
        </div>
        @endif
        
        <p>You can now close this window and return to the app.</p>
        
        <script>
            // Auto-close after 5 seconds if opened in popup
            if (window.opener) {
                setTimeout(() => {
                    window.close();
                }, 5000);
            }
        </script>
    </div>
</body>
</html>
