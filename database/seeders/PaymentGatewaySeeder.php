<?php

namespace Database\Seeders;

use App\Models\PaymentGatewaySetting;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // URWAY Payment Gateway Configuration
        PaymentGatewaySetting::updateOrCreate(
            ['gateway_name' => 'Urway'],
            [
                'api_key' => '75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef',
                'merchant_id' => 'gatherpoin',
                'terminal_id' => 'gatherpoin',
                'terminal_password' => 'gather@4512',
                'base_url' => 'https://payments-dev.urway-tech.com', // Sandbox URL
                'currency' => 'SAR',
                'callback_url' => config('app.url') . '/api/payments/urway/callback',
                'is_active' => true,
            ]
        );

        $this->command->info('Payment gateway settings seeded successfully!');
        $this->command->info('Using production URL: https://payments.urway-tech.com');
        $this->command->info('Note: Switch to sandbox URL for testing: https://payments-dev.urway-tech.com');
    }
}
