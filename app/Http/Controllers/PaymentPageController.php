<?php

namespace App\Http\Controllers;

use App\Models\PaymentTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentPageController extends Controller
{
    /**
     * Payment success page
     */
    public function success(Request $request)
    {
        $paymentId = $request->get('payment_id');
        
        Log::info('Payment Success Page Accessed', [
            'payment_id' => $paymentId,
            'all_params' => $request->all()
        ]);

        $paymentTransaction = null;
        if ($paymentId) {
            $paymentTransaction = PaymentTransaction::where('payment_id', $paymentId)->first();
        }

        return view('payment.success', [
            'payment_id' => $paymentId,
            'payment' => $paymentTransaction
        ]);
    }

    /**
     * Payment error page
     */
    public function error(Request $request)
    {
        $paymentId = $request->get('payment_id');
        
        Log::info('Payment Error Page Accessed', [
            'payment_id' => $paymentId,
            'all_params' => $request->all()
        ]);

        $paymentTransaction = null;
        if ($paymentId) {
            $paymentTransaction = PaymentTransaction::where('payment_id', $paymentId)->first();
        }

        return view('payment.error', [
            'payment_id' => $paymentId,
            'payment' => $paymentTransaction,
            'error_message' => $request->get('error', 'Payment failed')
        ]);
    }
}
