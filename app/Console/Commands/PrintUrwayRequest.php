<?php

namespace App\Console\Commands;

use App\Models\PaymentGatewaySetting;
use Illuminate\Console\Command;

class PrintUrwayRequest extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'print:urway-request {--amount=100} {--email=<EMAIL>}';

    /**
     * The console command description.
     */
    protected $description = 'Print the exact JSON request data being sent to URWAY for support purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📋 URWAY JSON Request Data for Support');
        $this->info('=====================================');
        $this->newLine();

        $config = PaymentGatewaySetting::getUrwayConfig();
        
        if (!$config) {
            $this->error('❌ URWAY configuration not found!');
            return 1;
        }

        // Test payment data
        $paymentData = [
            'payment_id' => 'SUPPORT_' . time(),
            'amount' => $this->option('amount'),
            'customer_email' => $this->option('email'),
            'reservation_id' => 123,
            'user_id' => 456,
        ];

        // Generate hash exactly as in service
        $amount = $paymentData['amount'];
        $currency = $config->currency ?? 'SAR';
        
        $hashString = $paymentData['payment_id'] . '|' . 
                     $config->terminal_id . '|' . 
                     $config->terminal_password . '|' . 
                     $config->api_key . '|' . 
                     $amount . '|' . 
                     $currency;

        $requestHash = hash('sha256', $hashString);

        // Build request data exactly as in service
        $requestData = [
            'trackid' => $paymentData['payment_id'],
            'terminalId' => $config->terminal_id,
            'password' => $config->terminal_password,
            'action' => '1', // Purchase action
            'merchantIp' => '127.0.0.1',
            'currency' => $config->currency ?? 'SAR',
            'country' => 'SA',
            'amount' => $paymentData['amount'],
            'customerEmail' => $paymentData['customer_email'] ?? '',
            'udf1' => (string)($paymentData['reservation_id'] ?? ''),
            'udf2' => (string)($paymentData['user_id'] ?? ''),
            'udf3' => 'reservation_payment',
            'udf4' => '',
            'udf5' => '',
            'requestHash' => $requestHash,
        ];

        // Print configuration info
        $this->info('🔧 Configuration:');
        $this->table(['Setting', 'Value'], [
            ['Base URL', $config->base_url],
            ['Terminal ID', $config->terminal_id],
            ['Currency', $config->currency],
            ['Endpoint', '/URWAYPGService/transaction/jsonProcess/JSONrequest'],
        ]);

        $this->newLine();
        $this->info('🔐 Hash Generation:');
        $this->line('Hash String Format: trackid|terminalId|password|merchantKey|amount|currency');
        $this->line('Hash String: ' . $hashString);
        $this->line('Generated Hash: ' . $requestHash);

        $this->newLine();
        $this->info('📤 Complete JSON Request Data:');
        $this->line('==============================');
        
        // Print the exact JSON that will be sent
        $jsonRequest = json_encode($requestData, JSON_PRETTY_PRINT);
        $this->line($jsonRequest);
        
        $this->newLine();
        $this->info('📋 Request Headers:');
        $this->line('Content-Type: application/json');
        $this->line('Accept: application/json');
        
        $this->newLine();
        $this->info('🌐 Full Request Details:');
        $this->line('Method: POST');
        $this->line('URL: ' . $config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest');
        $this->line('Body: ' . $jsonRequest);

        $this->newLine();
        $this->info('📝 For URWAY Support:');
        $this->line('Please share the above JSON request data with URWAY support.');
        $this->line('This is the exact format and data being sent to their API.');
        
        // Also save to a file for easy sharing
        $filename = 'urway_request_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents(storage_path('logs/' . $filename), $jsonRequest);
        $this->info("💾 Request data saved to: storage/logs/{$filename}");

        return 0;
    }
}
