<?php

namespace App\Console\Commands;

use App\Models\PaymentGatewaySetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TestUrwayCredentials extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:urway-credentials';

    /**
     * The console command description.
     */
    protected $description = 'Test URWAY credentials with minimal request';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing URWAY Credentials...');
        $this->newLine();

        $config = PaymentGatewaySetting::getUrwayConfig();
        
        if (!$config) {
            $this->error('❌ URWAY configuration not found!');
            return 1;
        }

        // Test with minimal inquiry request (action 8)
        $trackId = 'TEST_' . time();
        
        $requestData = [
            'trackid' => $trackId,
            'terminalId' => $config->terminal_id,
            'password' => $config->terminal_password,
            'action' => '8', // Inquiry - should work even for non-existent transaction
            'merchantIp' => '127.0.0.1',
        ];

        $this->info('Testing with inquiry request (action 8)...');
        $this->table(['Field', 'Value'], [
            ['Track ID', $trackId],
            ['Terminal ID', $config->terminal_id],
            ['Password', str_repeat('*', strlen($config->terminal_password))],
            ['Action', '8 (Inquiry)'],
            ['Base URL', $config->base_url],
        ]);

        try {
            Log::info('URWAY Credentials Test Request', [
                'url' => $config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest',
                'data' => array_merge($requestData, ['password' => '***'])
            ]);

            // Try form-encoded request
            $response = Http::timeout(30)
                ->asForm()
                ->post($config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest', $requestData);

            $this->info('Response Status: ' . $response->status());
            $this->info('Response Body: ' . $response->body());

            if ($response->successful()) {
                $responseData = $response->json();
                
                if (isset($responseData['result'])) {
                    $result = $responseData['result'];
                    $responseCode = $responseData['responseCode'] ?? 'N/A';
                    
                    $this->table(['Field', 'Value'], [
                        ['Result', $result],
                        ['Response Code', $responseCode],
                        ['Reason', $responseData['reason'] ?? 'N/A'],
                        ['Terminal ID', $responseData['terminalid'] ?? 'N/A'],
                    ]);

                    if ($responseCode === '601') {
                        $this->error('❌ Authentication Error (601) - Check credentials');
                        $this->info('This usually means:');
                        $this->info('- Terminal ID is incorrect');
                        $this->info('- Terminal Password is incorrect');
                        $this->info('- Account is not activated');
                        $this->info('- IP is not whitelisted (if required)');
                    } elseif ($result === 'UnSuccessful' && $responseCode !== '601') {
                        $this->warn('⚠️  Transaction not found (expected for test)');
                        $this->info('✅ Credentials appear to be working');
                    } else {
                        $this->info('✅ Response received - credentials working');
                    }
                } else {
                    $this->error('❌ Unexpected response format');
                }
            } else {
                $this->error('❌ HTTP Error: ' . $response->status());
                $this->error('Response: ' . $response->body());
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception: ' . $e->getMessage());
            Log::error('URWAY Credentials Test Failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        $this->newLine();
        $this->info('Test completed!');
        
        return 0;
    }
}
