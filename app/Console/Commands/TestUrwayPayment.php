<?php

namespace App\Console\Commands;

use App\Services\UrwayPaymentService;
use App\Models\PaymentGatewaySetting;
use Illuminate\Console\Command;
use Exception;

class TestUrwayPayment extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:urway-payment {--amount=10.00}';

    /**
     * The console command description.
     */
    protected $description = 'Test URWAY payment integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing URWAY Payment Integration...');
        $this->newLine();

        // Check configuration
        $this->info('1. Checking URWAY Configuration...');
        $config = PaymentGatewaySetting::getUrwayConfig();
        
        if (!$config) {
            $this->error('❌ URWAY configuration not found!');
            $this->info('Run: php artisan db:seed --class=PaymentGatewaySeeder');
            return 1;
        }

        $this->info('✅ URWAY configuration found');
        $this->table(['Field', 'Value', 'Status'], [
            ['Terminal ID', $config->terminal_id, !empty($config->terminal_id) ? '✅' : '❌'],
            ['Password', str_repeat('*', strlen($config->terminal_password)), !empty($config->terminal_password) ? '✅' : '❌'],
            ['API Key', substr($config->api_key, 0, 10) . '...', !empty($config->api_key) ? '✅' : '❌'],
            ['Base URL', $config->base_url, !empty($config->base_url) ? '✅' : '❌'],
            ['Currency', $config->currency, !empty($config->currency) ? '✅' : '❌'],
            ['Active', $config->is_active ? 'Yes' : 'No', $config->is_active ? '✅' : '❌'],
        ]);

        $this->newLine();

        // Test payment initiation
        $this->info('2. Testing Payment Initiation...');
        
        try {
            $urwayService = new UrwayPaymentService();
            
            $testPaymentData = [
                'payment_id' => 'TEST_' . uniqid(),
                'amount' => $this->option('amount'),
                'customer_email' => '<EMAIL>',
                'reservation_id' => 1,
                'user_id' => 1,
            ];

            $this->info('Test payment data:');
            $this->table(['Field', 'Value'], [
                ['Payment ID', $testPaymentData['payment_id']],
                ['Amount', $testPaymentData['amount'] . ' SAR'],
                ['Email', $testPaymentData['customer_email']],
            ]);

            $result = $urwayService->initiatePayment($testPaymentData);

            if ($result['success']) {
                $this->info('✅ Payment initiation successful!');
                $this->table(['Field', 'Value'], [
                    ['Payment URL', $result['payment_url'] ?? 'N/A'],
                    ['Payment ID', $result['payment_id'] ?? 'N/A'],
                    ['Transaction ID', $result['transaction_id'] ?? 'N/A'],
                ]);
                
                if (isset($result['payment_url'])) {
                    $this->info('🌐 You can test the payment at: ' . $result['payment_url']);
                }
            } else {
                $this->error('❌ Payment initiation failed!');
                $this->error('Error: ' . ($result['error'] ?? 'Unknown error'));
            }

        } catch (Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            $this->newLine();
            $this->info('Check logs for more details: storage/logs/laravel.log');
        }

        $this->newLine();
        $this->info('Test completed!');
        
        return 0;
    }
}
