<?php

namespace App\Console\Commands;

use App\Models\PaymentGatewaySetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupProduction extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'setup:production {--domain=}';

    /**
     * The console command description.
     */
    protected $description = 'Setup application for production deployment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up Gather Point for Production...');
        $this->newLine();

        // Get domain
        $domain = $this->option('domain') ?: $this->ask('What is your production domain? (e.g., api.gatherpoint.com)');
        
        if (!$domain) {
            $this->error('❌ Domain is required for production setup');
            return 1;
        }

        // Validate domain format
        if (!filter_var('https://' . $domain, FILTER_VALIDATE_URL)) {
            $this->error('❌ Invalid domain format');
            return 1;
        }

        $this->info("🌐 Setting up for domain: {$domain}");
        $this->newLine();

        // 1. Update payment gateway settings
        $this->info('1. Configuring URWAY Payment Gateway...');
        
        $config = PaymentGatewaySetting::where('gateway_name', 'Urway')->first();
        if ($config) {
            $config->update([
                'base_url' => 'https://payments.urway-tech.com',
                'callback_url' => "https://{$domain}/api/payments/urway/callback",
            ]);
            $this->info('✅ URWAY configured for production');
        } else {
            $this->warn('⚠️  URWAY configuration not found. Run: php artisan db:seed --class=PaymentGatewaySeeder');
        }

        // 2. Environment checks
        $this->info('2. Checking environment configuration...');
        
        $envChecks = [
            'APP_ENV' => env('APP_ENV'),
            'APP_DEBUG' => env('APP_DEBUG'),
            'APP_URL' => env('APP_URL'),
        ];

        $this->table(['Setting', 'Current Value', 'Recommended'], [
            ['APP_ENV', $envChecks['APP_ENV'], 'production'],
            ['APP_DEBUG', $envChecks['APP_DEBUG'] ? 'true' : 'false', 'false'],
            ['APP_URL', $envChecks['APP_URL'], "https://{$domain}"],
        ]);

        // 3. Security recommendations
        $this->info('3. Security Checklist:');
        $securityItems = [
            'SSL certificate installed and configured',
            'HTTPS enforced for all requests',
            'Database credentials secured',
            'API keys and secrets in environment variables',
            'File permissions properly set (755 for directories, 644 for files)',
            'Storage and bootstrap/cache directories writable',
        ];

        foreach ($securityItems as $item) {
            $this->line("   • {$item}");
        }

        // 4. Performance optimizations
        $this->newLine();
        $this->info('4. Running production optimizations...');
        
        try {
            Artisan::call('config:cache');
            $this->info('✅ Configuration cached');
            
            Artisan::call('route:cache');
            $this->info('✅ Routes cached');
            
            Artisan::call('view:cache');
            $this->info('✅ Views cached');
            
        } catch (\Exception $e) {
            $this->warn('⚠️  Some optimizations failed: ' . $e->getMessage());
        }

        // 5. URWAY specific setup
        $this->newLine();
        $this->info('5. URWAY Production Requirements:');
        
        $urwayRequirements = [
            "Domain {$domain} must be whitelisted with URWAY",
            "Server IP address may need to be whitelisted",
            "Callback URL configured: https://{$domain}/api/payments/urway/callback",
            "Terminal ID 'gatherpoin' must be activated for production",
            "Merchant account must be approved and active",
        ];

        foreach ($urwayRequirements as $requirement) {
            $this->line("   • {$requirement}");
        }

        // 6. Test commands
        $this->newLine();
        $this->info('6. Testing Commands:');
        $this->line("   php artisan test:urway-credentials");
        $this->line("   php artisan test:urway-payment --amount=1.00");

        // 7. Final recommendations
        $this->newLine();
        $this->info('🎯 Next Steps:');
        $this->line('1. Update your .env file with production values');
        $this->line('2. Contact URWAY support to activate production account');
        $this->line('3. Test payment flow in production environment');
        $this->line('4. Monitor logs for any issues');
        $this->line('5. Set up monitoring and alerting');

        $this->newLine();
        $this->info('📋 Production Setup Summary:');
        $this->table(['Component', 'Status'], [
            ['URWAY Gateway', '✅ Configured'],
            ['Payment APIs', '✅ Ready'],
            ['Callback Handling', '✅ Ready'],
            ['Success/Error Pages', '✅ Ready'],
            ['Database Schema', '✅ Ready'],
            ['Caching', '✅ Optimized'],
        ]);

        $this->newLine();
        $this->info('🚀 Production setup completed!');
        $this->warn('⚠️  Remember to test thoroughly before going live!');

        return 0;
    }
}
