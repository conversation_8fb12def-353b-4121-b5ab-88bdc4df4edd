<?php

namespace App\Console\Commands;

use App\Services\UrwayPaymentService;
use Illuminate\Console\Command;

class MakeTestPayment extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'make:test-payment {--amount=50} {--email=<EMAIL>} {--open}';

    /**
     * The console command description.
     */
    protected $description = 'Create a test payment and get the payment URL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Creating Test Payment...');
        $this->newLine();

        $amount = $this->option('amount');
        $email = $this->option('email');
        $shouldOpen = $this->option('open');

        // Create test payment data
        $paymentData = [
            'payment_id' => 'TEST_PAYMENT_' . time(),
            'amount' => $amount,
            'customer_email' => $email,
            'reservation_id' => 999,
            'user_id' => 1,
        ];

        $this->info('💳 Payment Details:');
        $this->table(['Field', 'Value'], [
            ['Payment ID', $paymentData['payment_id']],
            ['Amount', $amount . ' SAR'],
            ['Customer Email', $email],
            ['Reservation ID', $paymentData['reservation_id']],
        ]);

        try {
            $urwayService = new UrwayPaymentService();
            
            $this->info('📤 Initiating payment with URWAY...');
            $result = $urwayService->initiatePayment($paymentData);

            if ($result['success']) {
                $this->newLine();
                $this->info('✅ Payment initiated successfully!');
                
                $this->table(['Field', 'Value'], [
                    ['Payment URL', $result['payment_url']],
                    ['Payment ID', $result['payment_id']],
                    ['Transaction ID', $result['transaction_id']],
                ]);

                $this->newLine();
                $this->info('🌐 Test Payment URL:');
                $this->line($result['payment_url']);
                
                $this->newLine();
                $this->info('📋 Next Steps:');
                $this->line('1. Copy the payment URL above');
                $this->line('2. Open it in your browser');
                $this->line('3. Complete the test payment using URWAY test cards');
                $this->line('4. Verify the callback is received');
                
                $this->newLine();
                $this->info('💳 URWAY Test Cards:');
                $this->table(['Card Type', 'Card Number', 'CVV', 'Expiry'], [
                    ['MADA Test', '****************', '123', '12/25'],
                    ['Visa Test', '****************', '123', '12/25'],
                    ['Mastercard Test', '****************', '123', '12/25'],
                ]);

                if ($shouldOpen) {
                    $this->info('🌐 Opening payment URL in browser...');
                    if (PHP_OS_FAMILY === 'Darwin') {
                        exec('open ' . escapeshellarg($result['payment_url']));
                    } elseif (PHP_OS_FAMILY === 'Windows') {
                        exec('start ' . escapeshellarg($result['payment_url']));
                    } else {
                        exec('xdg-open ' . escapeshellarg($result['payment_url']));
                    }
                }

                $this->newLine();
                $this->warn('💡 Tip: Use --open flag to automatically open the payment URL in your browser');
                $this->warn('Example: php artisan make:test-payment --amount=100 --open');

            } else {
                $this->error('❌ Payment initiation failed!');
                $this->error('Error: ' . $result['message']);
                
                if (isset($result['error_code'])) {
                    $this->error('Error Code: ' . $result['error_code']);
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            $this->error('File: ' . $e->getFile() . ':' . $e->getLine());
        }

        return 0;
    }
}
