<?php

namespace App\Console\Commands;

use App\Models\PaymentGatewaySetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DebugUrwayRequest extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'debug:urway-request';

    /**
     * The console command description.
     */
    protected $description = 'Debug URWAY request format and compare with reference implementation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Debugging URWAY Request Format...');
        $this->newLine();

        $config = PaymentGatewaySetting::getUrwayConfig();
        
        if (!$config) {
            $this->error('❌ URWAY configuration not found!');
            return 1;
        }

        // Test data
        $trackId = 'DEBUG_' . time();
        $amount = 10;
        $currency = 'SAR';

        $this->info('📋 Test Data:');
        $this->table(['Field', 'Value'], [
            ['Track ID', $trackId],
            ['Amount', $amount],
            ['Currency', $currency],
            ['Terminal ID', $config->terminal_id],
        ]);

        // Generate hash exactly as reference implementation
        $hashString = $trackId . '|' . 
                     $config->terminal_id . '|' . 
                     $config->terminal_password . '|' . 
                     $config->api_key . '|' . 
                     $amount . '|' . 
                     $currency;

        $requestHash = hash('sha256', $hashString);

        $this->newLine();
        $this->info('🔐 Hash Generation:');
        $this->line('Hash String: ' . $hashString);
        $this->line('Hash Length: ' . strlen($hashString));
        $this->line('Generated Hash: ' . $requestHash);

        // Build request data exactly as reference
        $requestData = [
            'trackid' => $trackId,
            'terminalId' => $config->terminal_id,
            'password' => $config->terminal_password,
            'action' => '1',
            'merchantIp' => '127.0.0.1',
            'currency' => $currency,
            'country' => 'SA',
            'amount' => $amount,
            'customerEmail' => '<EMAIL>',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'requestHash' => $requestHash,
        ];

        $this->newLine();
        $this->info('📤 Request Data:');
        foreach ($requestData as $key => $value) {
            if (in_array($key, ['password', 'requestHash'])) {
                $displayValue = str_repeat('*', min(strlen($value), 20));
            } else {
                $displayValue = $value;
            }
            $this->line("  {$key}: {$displayValue}");
        }

        // Test different endpoints and methods
        $endpoints = [
            'JSON POST' => [
                'method' => 'POST',
                'url' => $config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest',
                'headers' => ['Content-Type' => 'application/json'],
                'data' => $requestData
            ],
            'Form POST' => [
                'method' => 'POST', 
                'url' => $config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest',
                'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                'data' => $requestData
            ]
        ];

        foreach ($endpoints as $name => $endpoint) {
            $this->newLine();
            $this->info("🌐 Testing: {$name}");
            $this->line("URL: {$endpoint['url']}");

            try {
                if ($name === 'JSON POST') {
                    $response = Http::timeout(30)
                        ->withHeaders($endpoint['headers'])
                        ->post($endpoint['url'], $endpoint['data']);
                } else {
                    $response = Http::timeout(30)
                        ->asForm()
                        ->post($endpoint['url'], $endpoint['data']);
                }

                $this->line("Status: {$response->status()}");
                $this->line("Response: {$response->body()}");

                if ($response->successful()) {
                    $responseData = $response->json();
                    if (isset($responseData['result'])) {
                        $this->line("Result: {$responseData['result']}");
                        $this->line("Response Code: " . ($responseData['responseCode'] ?? 'N/A'));
                        $this->line("Reason: " . ($responseData['reason'] ?? 'N/A'));
                    }
                }

            } catch (\Exception $e) {
                $this->error("Error: {$e->getMessage()}");
            }
        }

        // Test with minimal data (like reference package)
        $this->newLine();
        $this->info('🧪 Testing Minimal Request (Reference Style):');
        
        $minimalData = [
            'trackid' => $trackId,
            'terminalId' => $config->terminal_id,
            'password' => $config->terminal_password,
            'action' => '1',
            'amount' => $amount,
            'currency' => $currency,
            'requestHash' => $requestHash,
        ];

        try {
            $response = Http::timeout(30)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post($config->base_url . '/URWAYPGService/transaction/jsonProcess/JSONrequest', $minimalData);

            $this->line("Minimal Request Status: {$response->status()}");
            $this->line("Minimal Request Response: {$response->body()}");

        } catch (\Exception $e) {
            $this->error("Minimal Request Error: {$e->getMessage()}");
        }

        $this->newLine();
        $this->info('🔍 Debug completed!');
        $this->warn('💡 Check the responses above to identify the correct format');
        
        return 0;
    }
}
