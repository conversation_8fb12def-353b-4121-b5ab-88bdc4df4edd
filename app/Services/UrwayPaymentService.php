<?php

namespace App\Services;

use App\Models\PaymentGatewaySetting;
use App\Models\PaymentTransaction;
use App\Models\Admin\ServiceCategoryItemReservation;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class UrwayPaymentService
{
    private $config;
    private $baseUrl;

    public function __construct()
    {
        $this->config = PaymentGatewaySetting::getUrwayConfig();
        
        if (!$this->config) {
            throw new Exception('URWAY payment gateway is not configured');
        }

        $this->baseUrl = $this->config->base_url;
    }

    /**
     * Initiate payment with URWAY
     */
    public function initiatePayment(array $paymentData): array
    {
        try {
            // Validate required configuration
            $this->validateConfiguration();

            // Generate request hash first
            $requestHash = $this->generateRequestHash($paymentData);

            // Prepare callback URLs
            $baseUrl = config('app.url');
            $errorUrl = $baseUrl . '/payment/error?payment_id=' . $paymentData['payment_id'];

            $requestData = [
                'trackid' => $paymentData['payment_id'],
                'terminalId' => $this->config->terminal_id,
                'customerEmail' => $paymentData['customer_email'] ?? '',
                'action' => '1', // Purchase
                'merchantIp' => $this->getMerchantIp(),
                'password' => $this->config->terminal_password,
                'currency' => $this->config->currency ?? 'SAR',
                'country' => 'SA',
                'amount' => number_format($paymentData['amount'], 2, '.', ''),
                'udf1' => (string)($paymentData['reservation_id'] ?? ''),
                'udf2' => (string)($paymentData['user_id'] ?? ''),
                'udf3' => 'reservation_payment',
                'udf4' => '',
                'udf5' => '',
                'requestHash' => $requestHash,
                // Add callback URLs
                'responseURL' => $this->config->callback_url ?? '',
                'errorURL' => $errorUrl,
            ];

            Log::info('URWAY Payment Initiation Request', [
                'payment_id' => $paymentData['payment_id'],
                'request_data' => array_merge($requestData, ['password' => '***', 'requestHash' => '***'])
            ]);

            // URWAY expects JSON format (based on reference implementation)
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl . '/URWAYPGService/transaction/jsonProcess/JSONrequest', $requestData);

            Log::info('URWAY Raw Response', [
                'status' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('URWAY Payment Initiation Response', [
                    'payment_id' => $paymentData['payment_id'],
                    'response' => $responseData
                ]);

                // Check if payment initiation was successful (based on reference implementation)
                if (isset($responseData['result']) && strtoupper($responseData['result']) === 'SUCCESSFUL' &&
                    isset($responseData['payid']) && isset($responseData['targetUrl'])) {

                    // Build payment URL as per reference implementation
                    $paymentUrl = $responseData['targetUrl'] . '?paymentid=' . $responseData['payid'];

                    return [
                        'success' => true,
                        'payment_url' => $paymentUrl,
                        'payment_id' => $responseData['payid'],
                        'transaction_id' => $responseData['tranid'] ?? $responseData['payid'],
                        'response_data' => $responseData,
                    ];
                } elseif (isset($responseData['payid']) && isset($responseData['targetUrl'])) {
                    // Fallback for different response format
                    $paymentUrl = $responseData['targetUrl'] . '?paymentid=' . $responseData['payid'];

                    return [
                        'success' => true,
                        'payment_url' => $paymentUrl,
                        'payment_id' => $responseData['payid'],
                        'transaction_id' => $responseData['tranid'] ?? $responseData['payid'],
                        'response_data' => $responseData,
                    ];
                } else {
                    // Log the full response for debugging
                    Log::error('URWAY Payment Initiation Failed', [
                        'payment_id' => $paymentData['payment_id'],
                        'response' => $responseData,
                        'result' => $responseData['result'] ?? 'N/A',
                        'reason' => $responseData['reason'] ?? 'N/A',
                        'responseCode' => $responseData['responseCode'] ?? 'N/A'
                    ]);

                    $errorMessage = $responseData['reason'] ?? 'Payment initiation failed';
                    if (isset($responseData['responseCode'])) {
                        $errorMessage .= ' (Code: ' . $responseData['responseCode'] . ')';
                    }

                    return [
                        'success' => false,
                        'error' => $errorMessage,
                        'response_data' => $responseData,
                    ];
                }
            } else {
                Log::error('URWAY HTTP Error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'payment_id' => $paymentData['payment_id']
                ]);

                throw new Exception('URWAY API request failed with status ' . $response->status() . ': ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('URWAY Payment Initiation Failed', [
                'payment_id' => $paymentData['payment_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'config_check' => [
                    'has_terminal_id' => !empty($this->config->terminal_id ?? ''),
                    'has_password' => !empty($this->config->terminal_password ?? ''),
                    'has_api_key' => !empty($this->config->api_key ?? ''),
                    'base_url' => $this->config->base_url ?? 'not_set'
                ]
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response_data' => null,
            ];
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $paymentId, string $transactionId): array
    {
        try {
            $requestData = [
                'trackid' => $paymentId,
                'terminalId' => $this->config->terminal_id,
                'password' => $this->config->terminal_password,
                'action' => '8', // Inquiry
                'merchantIp' => request()->ip(),
            ];

            $response = Http::timeout(30)->post($this->baseUrl . '/URWAYPGService/transaction/jsonProcess/JSONrequest', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('URWAY Payment Verification Response', [
                    'payment_id' => $paymentId,
                    'transaction_id' => $transactionId,
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'status' => $this->mapUrwayStatus($responseData['result'] ?? ''),
                    'amount' => $responseData['amt'] ?? 0,
                    'currency' => $responseData['currency'] ?? 'SAR',
                    'payment_method' => $this->mapPaymentMethod($responseData['paymentMethod'] ?? ''),
                    'response_data' => $responseData,
                ];
            } else {
                throw new Exception('URWAY verification request failed: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('URWAY Payment Verification Failed', [
                'payment_id' => $paymentId,
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle URWAY callback/webhook
     */
    public function handleCallback(array $callbackData): array
    {
        try {
            Log::info('URWAY Callback Received', $callbackData);

            // Verify callback authenticity
            if (!$this->verifyCallbackHash($callbackData)) {
                throw new Exception('Invalid callback hash');
            }

            $paymentId = $callbackData['trackid'] ?? null;
            $result = $callbackData['result'] ?? '';
            $amount = $callbackData['amt'] ?? 0;

            if (!$paymentId) {
                throw new Exception('Missing payment ID in callback');
            }

            return [
                'success' => true,
                'payment_id' => $paymentId,
                'status' => $this->mapUrwayStatus($result),
                'amount' => $amount,
                'currency' => $callbackData['currency'] ?? 'SAR',
                'payment_method' => $this->mapPaymentMethod($callbackData['paymentMethod'] ?? ''),
                'transaction_id' => $callbackData['tranid'] ?? null,
                'response_data' => $callbackData,
            ];
        } catch (Exception $e) {
            Log::error('URWAY Callback Processing Failed', [
                'callback_data' => $callbackData,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Validate URWAY configuration
     */
    private function validateConfiguration(): void
    {
        $required = ['terminal_id', 'terminal_password', 'api_key', 'base_url'];

        foreach ($required as $field) {
            if (empty($this->config->$field)) {
                throw new Exception("URWAY configuration missing: {$field}");
            }
        }
    }

    /**
     * Get merchant IP address
     */
    private function getMerchantIp(): string
    {
        // Try to get real IP behind proxy
        $ip = request()->header('X-Forwarded-For')
            ?? request()->header('X-Real-IP')
            ?? request()->ip()
            ?? '127.0.0.1';

        // If multiple IPs, get the first one
        if (str_contains($ip, ',')) {
            $ip = trim(explode(',', $ip)[0]);
        }

        return $ip;
    }

    /**
     * Generate request hash for URWAY (based on reference implementation)
     */
    private function generateRequestHash(array $paymentData): string
    {
        // Format amount exactly as in reference implementation
        $amount = $paymentData['amount'];
        $currency = $this->config->currency ?? 'SAR';

        // Hash format from reference: trackid|terminal_id|password|merchant_key|amount|currency
        $hashString = $paymentData['payment_id'] . '|' .
                     $this->config->terminal_id . '|' .
                     $this->config->terminal_password . '|' .
                     $this->config->api_key . '|' .
                     $amount . '|' .
                     $currency;

        $hash = hash('sha256', $hashString);

        Log::debug('URWAY Hash Generation (Reference Format)', [
            'payment_id' => $paymentData['payment_id'],
            'terminal_id' => $this->config->terminal_id,
            'hash_string_format' => 'trackid|terminal_id|password|merchant_key|amount|currency',
            'amount_raw' => $amount,
            'currency' => $currency,
            'hash_string_length' => strlen($hashString),
            'hash' => $hash
        ]);

        return $hash;
    }

    /**
     * Verify callback hash
     */
    private function verifyCallbackHash(array $callbackData): bool
    {
        $expectedHash = $callbackData['responseHash'] ?? '';
        
        if (!$expectedHash) {
            return false;
        }

        $hashString = ($callbackData['trackid'] ?? '') . '|' . 
                     $this->config->terminal_id . '|' . 
                     $this->config->terminal_password . '|' . 
                     $this->config->api_key . '|' . 
                     ($callbackData['amt'] ?? '') . '|' . 
                     ($callbackData['currency'] ?? '');

        $calculatedHash = hash('sha256', $hashString);

        return hash_equals($calculatedHash, $expectedHash);
    }

    /**
     * Map URWAY status to our internal status
     */
    private function mapUrwayStatus(string $urwayStatus): string
    {
        switch (strtoupper($urwayStatus)) {
            case 'CAPTURED':
            case 'SUCCESS':
                return 'completed';
            case 'FAILED':
            case 'DECLINED':
                return 'failed';
            case 'CANCELLED':
                return 'cancelled';
            case 'PENDING':
                return 'processing';
            default:
                return 'pending';
        }
    }

    /**
     * Map URWAY payment method to our internal format
     */
    private function mapPaymentMethod(string $urwayMethod): string
    {
        switch (strtolower($urwayMethod)) {
            case 'mada':
                return 'mada';
            case 'visa':
                return 'credit_card';
            case 'mastercard':
                return 'credit_card';
            case 'applepay':
                return 'apple_pay';
            case 'stcpay':
                return 'stc_pay';
            default:
                return 'other';
        }
    }

    /**
     * Check if URWAY is configured and active
     */
    public static function isActive(): bool
    {
        return PaymentGatewaySetting::isUrwayActive();
    }
}
